# RagHop系统Bing搜索操作详细分析

## 🔍 Bing搜索在RagHop中的作用

Bing搜索是RagHop系统的重要组成部分，用于获取实时的外部信息，补充知识库中可能缺失的最新内容。它与本地知识库检索形成互补，提供更全面的信息覆盖。

## 🌐 Bing搜索的完整操作流程

### 第一阶段：搜索请求构建

#### 核心函数：`search_bing(query)`

```python
def search_bing(query):
    """利用Bing搜索接口，用于检索与query相关的背景信息"""
    
    # 第一步：构建反爬虫请求头
    headers = {
        'Connection': 'keep-alive',
        'Accept-Encoding': 'gzip, deflate',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept': '*/*',
        'Referer': 'https://cn.bing.com/search?',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    # 第二步：构建搜索URL
    url = 'https://cn.bing.com/search?q=' + query + '&qs=n&form=QBRE'
    
    # 第三步：发送搜索请求
    r = requests.get(url, headers=headers)
```

#### 反爬虫策略：
- **User-Agent伪装**：模拟Chrome浏览器访问
- **完整请求头**：包含Connection、Accept等标准头部
- **Referer设置**：模拟从Bing首页跳转
- **中文语言偏好**：`zh-CN,zh;q=0.9`确保中文结果

### 第二阶段：搜索结果解析

#### 编码处理：
```python
# 自动检测和处理编码
try:
    encoding = chardet.detect(r.content)['encoding']
    r.encoding = encoding
    dom = etree.HTML(r.content.decode(encoding))
except:
    # 编码检测失败时使用默认解析
    dom = etree.HTML(r.content)
```

#### 链接提取：
```python
url_list = []
tmp_url = []

# 解析搜索结果页面，提取链接和标题
for sel in dom.xpath('//ol[@id="b_results"]/li/h2'):
    l = ''.join(sel.xpath('a/@href'))  # 提取链接
    title = ''.join(sel.xpath('a//text()')).split('-')[0].strip()  # 提取标题
    
    # 过滤条件：
    # 1. 必须是http链接
    # 2. 避免重复链接
    # 3. 排除doc文档链接
    if 'http' in l and l not in tmp_url and 'doc.' not in l:
        url_list.append([l, title])
        tmp_url.append(l)
```

### 第三阶段：网页内容抓取

#### 逐页内容抓取：
```python
for turl, title in url_list:
    try:
        # 访问目标页面，设置超时防止卡死
        tr = requests.get(turl, headers=headers, timeout=(5, 5))
        tdom = etree.HTML(tr.content.decode('utf-8'))
        
        # 提取页面中所有段落文本
        text = '\n'.join(tdom.xpath('//p/text()'))
        
        # 过滤内容过短的页面，确保有效信息
        if len(text) > 15:
            tmp = {
                'url': turl,      # 页面链接
                'text': text,     # 页面正文
                'title': title    # 页面标题
            }
            res.append(tmp)
    except Exception as e:
        # 单个页面抓取失败不影响其他页面
        print(f"抓取页面失败 {turl}: {e}")
        pass
```

#### 内容质量控制：
- **超时设置**：`timeout=(5, 5)`防止页面加载卡死
- **内容长度过滤**：只保留长度>15字符的内容
- **错误隔离**：单页失败不影响整体搜索
- **结构化存储**：URL、文本、标题分别保存

## 🧠 智能文本召回和排序

### 第四阶段：文本召回处理

#### 核心类：`TextRecallRank`

```python
class TextRecallRank():
    def __init__(self, cfg):
        self.topk = cfg.topk      # query关键词召回的数量
        self.topd = cfg.topd      # 召回文章的数量
        self.topt = cfg.topt      # 召回文本片段的数量
        self.maxlen = cfg.maxlen  # 召回文本片段的长度
        self.recall_way = cfg.recall_way  # 召回方式：keyword或embed
```

#### 两种召回策略：

### 策略1：关键词召回 (`rank_text_by_keywords`)

#### 第一步：查询分析
```python
def query_analyze(self, query):
    """使用jieba进行关键词提取"""
    keywords = jieba.analyse.extract_tags(query, topK=self.topk, withWeight=True)
    total_weight = self.topk / sum([r[1] for r in keywords])
    return keywords, total_weight
```

#### 第二步：标题召回
```python
def recall_title_score(self, title, keywords, total_weight):
    """计算query与标题的匹配度"""
    score = 0
    for item in keywords:
        kw, weight = item
        if kw in title:
            score += round(weight * total_weight, 4)
    return score
```

#### 第三步：文本片段召回
```python
def recall_text_score(self, text, keywords, total_weight):
    """计算query与text的匹配程度"""
    score = 0
    for item in keywords:
        kw, weight = item
        p11 = re.compile('%s' % kw)
        pr = p11.findall(text)
        score += round(weight * total_weight, 4)
    return score
```

### 策略2：语义向量召回 (`rank_text_by_text2vec`)

#### 第一步：标题向量化
```python
title_list = [query]
for line in data:
    title = line['title']
    title_list.append(title)

title_vectors = get_vector(title_list, 8)
title_score = get_sim(title_vectors)
```

#### 第二步：句子向量化
```python
sentence_list = [query]
for line in data:
    title = line['title']
    text = line['text']
    if title in recall_title_list:
        for ct in self.text_segmentate(text, self.maxlen, seps='\n。'):
            ct = re.sub('\s+', ' ', ct)
            if len(ct) >= 20:
                sentence_list.append(ct)

sentence_vectors = get_vector(sentence_list, 8)
sentence_score = get_sim(sentence_vectors)
```

### 第五阶段：文本分段处理

#### 智能文本分段：
```python
def text_segmentate(self, text, maxlen, seps='\n', strips=None):
    """将文本按照标点符号划分为若干个短句"""
    text = text.strip().strip(strips)
    if seps and len(text) > maxlen:
        pieces = text.split(seps[0])
        text, texts = '', []
        for i, p in enumerate(pieces):
            if text and p and len(text) + len(p) > maxlen - 1:
                texts.extend(self.text_segmentate(text, maxlen, seps[1:], strips))
                text = ''
            # 递归处理长文本
        return texts
    else:
        return [text]
```

#### 分段策略：
- **分隔符优先级**：`\n` → `。` → 其他标点
- **长度控制**：确保每段不超过maxlen
- **递归处理**：对超长文本进行递归分割
- **完整性保持**：尽量保持句子完整

## 🔄 RagHop中的集成使用

### 第六阶段：系统集成

#### 主要调用接口：`get_search_background()`

```python
def get_search_background(query: str, max_length: int = 1500) -> str:
    """联网搜索背景信息函数"""
    try:
        # 第一步：调用联网搜索功能
        from retrievor import q_searching
        search_results = q_searching(query)
        
        # 第二步：清理搜索结果文本
        cleaned_results = re.sub(r'\s+', ' ', search_results).strip()
        
        # 第三步：按长度限制截断结果
        return cleaned_results[:max_length]
    except Exception as e:
        print(f"联网搜索失败：{str(e)}")
        return ""
```

#### 在多跳推理中的使用：
```python
# 并行处理：知识库检索 + 联网搜索
with ThreadPoolExecutor(max_workers=2) as executor:
    futures = {}
    
    if use_search:
        futures[executor.submit(get_search_background, question)] = "search"
        
    if os.path.exists(index_path):
        if multi_hop:
            futures[executor.submit(multi_hop_generate_answer, question, kb_name)] = "rag"
        else:
            futures[executor.submit(simple_generate_answer, question, kb_name)] = "simple"
```

## 🎯 Bing搜索的技术特点

### 1. 反爬虫能力强
- **完整的浏览器模拟**：User-Agent、Referer、Accept等
- **编码自适应**：自动检测和处理各种编码
- **超时控制**：防止网络异常导致的阻塞

### 2. 内容质量保证
- **多层过滤**：URL过滤、内容长度过滤、重复过滤
- **结构化提取**：XPath精确提取标题和正文
- **错误隔离**：单页失败不影响整体结果

### 3. 智能召回机制
- **双重策略**：关键词匹配 + 语义向量
- **分层召回**：标题召回 → 文本召回
- **可配置参数**：topk、topd、topt、maxlen

### 4. 系统集成优化
- **并行处理**：与知识库检索并行执行
- **长度控制**：结果截断避免过长
- **异常处理**：网络失败时优雅降级

## 📊 搜索效果优化

### 配置参数说明：
- **topk**：关键词提取数量，影响召回精度
- **topd**：召回文章数量，影响信息覆盖面
- **topt**：召回文本片段数量，影响最终结果长度
- **maxlen**：文本片段最大长度，影响内容完整性
- **recall_way**：召回方式选择，keyword vs embed

### 质量控制机制：
- **内容长度过滤**：>15字符的有效内容
- **重复链接过滤**：避免重复抓取
- **文档类型过滤**：排除doc等文档链接
- **编码错误处理**：多种编码尝试和容错

## 🚀 在医疗RAG中的价值

### 1. 实时信息补充
- **最新医疗资讯**：获取知识库中没有的最新信息
- **临床指南更新**：补充最新的诊疗指南
- **药物信息**：获取最新的药物信息和副作用

### 2. 信息覆盖扩展
- **多源信息整合**：结合本地知识库和网络信息
- **知识盲区补充**：填补知识库的空白领域
- **实时验证**：验证知识库信息的时效性

### 3. 多跳推理支持
- **信息缺口填补**：为多跳推理提供额外信息源
- **查询扩展**：支持后续查询的信息补充
- **答案验证**：提供外部信息进行交叉验证

这种完整的Bing搜索机制使得RagHop系统能够在保持本地知识库优势的同时，获得实时的外部信息补充，为用户提供更全面、更准确的医疗问答服务。
