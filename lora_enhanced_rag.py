#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LoRA增强的RagHop系统实现
基于原有RagHop系统，集成LoRA微调的本地大语言模型
"""

import torch
import os
import json
import numpy as np
from typing import List, Dict, Any, Optional, Generator
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel, LoraConfig, get_peft_model, TaskType
import faiss

# 导入原有的配置和工具函数
from config import Config
from rag import ReasoningRAG, vectorize_query

class LoRAEnhancedLLMClient:
    """LoRA增强的本地LLM客户端，替代原有的DeepSeekClient"""
    
    def __init__(self, 
                 base_model_name: str = "Qwen/Qwen2-7B-Instruct",
                 lora_adapter_path: Optional[str] = None,
                 device: str = "auto",
                 torch_dtype = torch.float16):
        """
        初始化LoRA增强的LLM客户端
        
        参数:
            base_model_name: 基础模型名称
            lora_adapter_path: LoRA适配器路径（如果存在）
            device: 设备配置
            torch_dtype: 模型精度
        """
        self.base_model_name = base_model_name
        self.lora_adapter_path = lora_adapter_path
        self.device = device
        self.torch_dtype = torch_dtype
        
        print(f"🚀 初始化LoRA增强的LLM客户端...")
        print(f"📦 基础模型: {base_model_name}")
        if lora_adapter_path:
            print(f"🔧 LoRA适配器: {lora_adapter_path}")
        
        self._load_model()
    
    def _load_model(self):
        """加载模型和LoRA适配器"""
        try:
            # 1. 加载基础模型
            print("📥 加载基础模型...")
            self.base_model = AutoModelForCausalLM.from_pretrained(
                self.base_model_name,
                torch_dtype=self.torch_dtype,
                device_map=self.device,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            # 2. 加载分词器
            print("📝 加载分词器...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_name,
                trust_remote_code=True,
                padding_side="left"
            )
            
            # 设置pad_token
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # 3. 加载LoRA适配器（如果存在）
            if self.lora_adapter_path and os.path.exists(self.lora_adapter_path):
                print(f"🔧 加载LoRA适配器: {self.lora_adapter_path}")
                self.model = PeftModel.from_pretrained(
                    self.base_model,
                    self.lora_adapter_path,
                    torch_dtype=self.torch_dtype
                )
                print("✅ LoRA适配器加载成功！")
            else:
                print("⚠️  未找到LoRA适配器，使用基础模型")
                self.model = self.base_model
            
            # 4. 设置为评估模式
            self.model.eval()
            print("✅ 模型加载完成！")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {str(e)}")
            raise e
    
    def generate_answer(self, system_prompt: str, user_prompt: str) -> str:
        """
        生成答案，兼容原有DeepSeekClient的接口
        
        参数:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        返回:
            生成的答案文本
        """
        try:
            # 构建完整的提示词
            full_prompt = f"""<|im_start|>system
{system_prompt}<|im_end|>
<|im_start|>user
{user_prompt}<|im_end|>
<|im_start|>assistant
"""
            
            # 分词
            inputs = self.tokenizer(
                full_prompt,
                return_tensors="pt",
                max_length=2048,
                truncation=True,
                padding=True
            ).to(self.model.device)
            
            # 生成回答
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=512,
                    temperature=0.1,
                    top_p=0.9,
                    do_sample=True,
                    pad_token_id=self.tokenizer.eos_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # 解码答案
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],
                skip_special_tokens=True
            )
            
            return response.strip()
            
        except Exception as e:
            return f"生成答案时出错: {str(e)}"
    
    def generate_reasoning(self, system_prompt: str, user_prompt: str) -> str:
        """专门用于推理分析的生成方法"""
        return self.generate_answer(system_prompt, user_prompt)


class LoRAEnhancedReasoningRAG(ReasoningRAG):
    """集成LoRA的多跳推理RAG系统"""
    
    def __init__(self, 
                 index_path: str,
                 metadata_path: str,
                 base_model: str = "Qwen/Qwen2-7B-Instruct",
                 lora_adapter_path: Optional[str] = None,
                 max_hops: int = 3,
                 initial_candidates: int = 5,
                 refined_candidates: int = 3,
                 verbose: bool = False):
        """
        初始化LoRA增强的推理RAG系统
        
        参数:
            index_path: FAISS索引路径
            metadata_path: 元数据路径
            base_model: 基础模型名称
            lora_adapter_path: LoRA适配器路径
            max_hops: 最大推理跳数
            initial_candidates: 初始检索候选数量
            refined_candidates: 精炼检索候选数量
            verbose: 是否打印详细日志
        """
        
        # 先初始化LoRA客户端
        print("🔧 初始化LoRA增强的LLM客户端...")
        self.llm_client = LoRAEnhancedLLMClient(
            base_model_name=base_model,
            lora_adapter_path=lora_adapter_path
        )
        
        # 调用父类初始化（但不使用其LLM客户端）
        self.index_path = index_path
        self.metadata_path = metadata_path
        self.max_hops = max_hops
        self.initial_candidates = initial_candidates
        self.refined_candidates = refined_candidates
        self.verbose = verbose
        
        # 加载FAISS索引和元数据
        self._load_resources()
        
        print("✅ LoRA增强的ReasoningRAG初始化完成！")
    
    def _generate_reasoning(self, 
                           query: str, 
                           retrieved_chunks: List[Dict[str, Any]], 
                           previous_queries: List[str], 
                           hop: int) -> Dict[str, Any]:
        """
        使用LoRA增强的LLM进行推理分析
        
        参数:
            query: 原始查询
            retrieved_chunks: 检索到的文档块
            previous_queries: 之前的查询列表
            hop: 当前跳数
            
        返回:
            推理结果字典
        """
        
        # 构建医疗专业的系统提示词
        system_prompt = """你是一个专业的医疗信息分析专家。你的任务是分析检索到的医疗文档，判断是否需要进一步检索信息来完整回答用户的问题。

请严格按照以下JSON格式输出：
{
    "analysis": "对当前信息的详细分析",
    "missing_info": "缺失的关键信息描述",
    "follow_up_queries": ["后续查询1", "后续查询2"],
    "is_sufficient": true/false
}

注意事项：
1. 分析要专业准确，基于医学知识
2. 如果信息不足，生成1-3个具体的后续查询
3. 确保后续查询能够获取缺失的关键信息
4. 严格遵循JSON格式"""
        
        # 构建用户提示词
        context_text = "\n".join([
            f"文档{i+1}: {chunk['chunk']}" 
            for i, chunk in enumerate(retrieved_chunks)
        ])
        
        previous_text = "\n".join([
            f"- {q}" for q in previous_queries
        ]) if previous_queries else "无"
        
        user_prompt = f"""原始查询: {query}
当前跳数: {hop}
已执行的查询: {previous_text}

检索到的医疗文档:
{context_text}

请分析以上医疗信息是否足够回答原始查询。如果不够，请生成1-3个后续查询来获取缺失的医疗信息。"""
        
        # 使用LoRA增强的LLM生成推理
        reasoning_text = self.llm_client.generate_reasoning(system_prompt, user_prompt)
        
        if self.verbose:
            print(f"🧠 推理分析 (跳数 {hop}): {reasoning_text}")
        
        # 解析推理结果
        return self._parse_reasoning_result(reasoning_text)
    
    def _synthesize_answer(self, query: str, all_chunks: List[Dict[str, Any]]) -> str:
        """
        使用LoRA增强的LLM合成最终答案
        
        参数:
            query: 原始查询
            all_chunks: 所有检索到的文档块
            
        返回:
            合成的最终答案
        """
        
        # 医疗专业的系统提示词
        system_prompt = """你是一个专业的医疗AI助手。请基于提供的医疗文档信息，为用户提供准确、专业、安全的医疗建议。

重要原则：
1. 提供准确的医疗信息，基于循证医学
2. 避免给出具体的诊断结论，强调专业医疗咨询的重要性
3. 不推荐未经验证的治疗方法
4. 使用通俗易懂的语言，便于患者理解
5. 承认知识的局限性，建议及时就医
6. 保护患者隐私和安全
7. 如果涉及紧急情况，明确建议立即就医"""
        
        # 整理所有检索到的信息
        all_context = "\n".join([
            f"医疗信息{i+1}: {chunk['chunk']}" 
            for i, chunk in enumerate(all_chunks)
        ])
        
        user_prompt = f"""基于以下医疗文档信息回答问题：

{all_context}

问题：{query}

请提供专业、准确、安全的医疗建议。记住要强调专业医疗咨询的重要性。"""
        
        # 使用LoRA增强的LLM生成最终答案
        answer = self.llm_client.generate_answer(system_prompt, user_prompt)
        
        if self.verbose:
            print(f"📝 最终答案: {answer}")
        
        return answer


def create_lora_enhanced_rag_system(kb_name: str, 
                                   lora_adapter_path: Optional[str] = None,
                                   base_model: str = "Qwen/Qwen2-7B-Instruct") -> LoRAEnhancedReasoningRAG:
    """
    创建LoRA增强的RAG系统
    
    参数:
        kb_name: 知识库名称
        lora_adapter_path: LoRA适配器路径
        base_model: 基础模型名称
        
    返回:
        LoRA增强的推理RAG系统实例
    """
    
    # 构建知识库路径
    kb_dir = os.path.join(Config.kb_base_dir, kb_name)
    index_path = os.path.join(kb_dir, f"{kb_name}.index")
    metadata_path = os.path.join(kb_dir, f"{kb_name}_metadata.json")
    
    # 检查文件是否存在
    if not os.path.exists(index_path):
        raise FileNotFoundError(f"FAISS索引文件不存在: {index_path}")
    if not os.path.exists(metadata_path):
        raise FileNotFoundError(f"元数据文件不存在: {metadata_path}")
    
    # 创建LoRA增强的RAG系统
    lora_rag = LoRAEnhancedReasoningRAG(
        index_path=index_path,
        metadata_path=metadata_path,
        base_model=base_model,
        lora_adapter_path=lora_adapter_path,
        max_hops=3,
        initial_candidates=5,
        refined_candidates=3,
        verbose=True
    )
    
    return lora_rag


# 示例使用函数
def example_usage():
    """LoRA增强RAG系统的使用示例"""
    
    print("🔥 LoRA增强的RagHop系统示例")
    print("=" * 50)
    
    try:
        # 创建LoRA增强的RAG系统
        lora_rag = create_lora_enhanced_rag_system(
            kb_name="medical_kb",  # 假设有一个医疗知识库
            lora_adapter_path="./medical_lora_adapter",  # LoRA适配器路径
            base_model="Qwen/Qwen2-7B-Instruct"
        )
        
        # 测试查询
        test_query = "糖尿病的早期症状有哪些？"
        print(f"🤔 测试查询: {test_query}")
        print("-" * 30)
        
        # 使用多跳推理回答问题
        response_generator = lora_rag.stream_retrieve_and_answer(test_query)
        
        print("📖 推理过程:")
        for chunk in response_generator:
            if chunk["type"] == "reasoning":
                print(f"🧠 推理 (跳数 {chunk['hop']}): {chunk['content']}")
            elif chunk["type"] == "retrieval":
                print(f"🔍 检索: {chunk['content']}")
            elif chunk["type"] == "final_answer":
                print(f"✅ 最终答案: {chunk['content']}")
                break
        
    except Exception as e:
        print(f"❌ 示例运行失败: {str(e)}")


if __name__ == "__main__":
    example_usage()
