# PDF中图片和表格处理方案详细分析

## 🔍 当前RagHop系统的处理现状

### 现有处理方式
```python
def extract_text_from_pdf(pdf_path: str) -> str:
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        page_text = page.get_text()  # 只提取纯文本
        text += page_text.encode('utf-8', errors='ignore').decode('utf-8')
    return text
```

**当前限制**：
- ❌ **只提取纯文本**：忽略图片中的文字信息
- ❌ **表格结构丢失**：表格被转换为无结构的文本
- ❌ **图表信息缺失**：图表、流程图等视觉信息完全丢失
- ❌ **布局信息丢失**：文档的空间布局关系被破坏

## 🚀 改进方案：多模态PDF处理系统

### 方案一：增强文本提取 + OCR识别

#### 1. 表格结构化提取
```python
def extract_tables_from_pdf(pdf_path: str) -> List[Dict]:
    """提取PDF中的表格并保持结构"""
    import tabula
    import camelot
    
    tables = []
    try:
        # 方法1：使用tabula-py提取表格
        tabula_tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)
        for i, table in enumerate(tabula_tables):
            tables.append({
                'type': 'table',
                'method': 'tabula',
                'page': i+1,
                'data': table.to_dict('records'),
                'markdown': table.to_markdown(),
                'text': table.to_string()
            })
    except Exception as e:
        print(f"Tabula表格提取失败: {e}")
    
    try:
        # 方法2：使用camelot提取表格（更精确）
        camelot_tables = camelot.read_pdf(pdf_path, pages='all')
        for table in camelot_tables:
            tables.append({
                'type': 'table',
                'method': 'camelot',
                'page': table.page,
                'accuracy': table.accuracy,
                'data': table.df.to_dict('records'),
                'markdown': table.df.to_markdown(),
                'text': table.df.to_string()
            })
    except Exception as e:
        print(f"Camelot表格提取失败: {e}")
    
    return tables
```

#### 2. 图片OCR文字识别
```python
def extract_images_with_ocr(pdf_path: str) -> List[Dict]:
    """提取PDF中的图片并进行OCR识别"""
    import fitz
    from PIL import Image
    import pytesseract
    import io
    
    images_data = []
    doc = fitz.open(pdf_path)
    
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        image_list = page.get_images()
        
        for img_index, img in enumerate(image_list):
            try:
                # 提取图片
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                if pix.n - pix.alpha < 4:  # 确保是RGB或灰度图
                    img_data = pix.tobytes("png")
                    img_pil = Image.open(io.BytesIO(img_data))
                    
                    # OCR识别图片中的文字
                    ocr_text = pytesseract.image_to_string(img_pil, lang='chi_sim+eng')
                    
                    if ocr_text.strip():  # 只保存有文字的图片
                        images_data.append({
                            'type': 'image',
                            'page': page_num + 1,
                            'index': img_index,
                            'ocr_text': ocr_text.strip(),
                            'size': (pix.width, pix.height)
                        })
                
                pix = None  # 释放内存
            except Exception as e:
                print(f"图片OCR处理失败 (页面{page_num+1}, 图片{img_index}): {e}")
    
    doc.close()
    return images_data
```

#### 3. 增强的PDF处理函数
```python
def enhanced_extract_from_pdf(pdf_path: str) -> Dict[str, Any]:
    """增强的PDF内容提取，包含文本、表格、图片"""
    
    # 1. 提取基础文本
    basic_text = extract_text_from_pdf(pdf_path)
    
    # 2. 提取表格
    tables = extract_tables_from_pdf(pdf_path)
    
    # 3. 提取图片中的文字
    images = extract_images_with_ocr(pdf_path)
    
    # 4. 整合所有内容
    all_content = []
    
    # 添加基础文本
    if basic_text.strip():
        all_content.append({
            'type': 'text',
            'content': basic_text,
            'source': 'direct_extraction'
        })
    
    # 添加表格内容
    for table in tables:
        all_content.append({
            'type': 'table',
            'content': table['markdown'],  # 使用Markdown格式保持结构
            'raw_data': table['data'],
            'page': table['page'],
            'source': f"table_{table['method']}"
        })
    
    # 添加图片OCR内容
    for image in images:
        all_content.append({
            'type': 'image_text',
            'content': image['ocr_text'],
            'page': image['page'],
            'source': 'ocr_extraction'
        })
    
    return {
        'basic_text': basic_text,
        'tables': tables,
        'images': images,
        'all_content': all_content,
        'total_elements': len(all_content)
    }
```

### 方案二：多模态向量化处理

#### 1. 分类向量化策略
```python
def multimodal_vectorize_content(content_data: Dict[str, Any]) -> List[Dict]:
    """多模态内容的分类向量化"""
    
    vectorized_chunks = []
    
    for item in content_data['all_content']:
        content_type = item['type']
        content_text = item['content']
        
        # 根据内容类型添加前缀，帮助模型理解上下文
        if content_type == 'table':
            prefixed_content = f"[表格数据]\n{content_text}"
            chunk_method = "table_extraction"
        elif content_type == 'image_text':
            prefixed_content = f"[图片文字]\n{content_text}"
            chunk_method = "ocr_extraction"
        else:
            prefixed_content = content_text
            chunk_method = "text_extraction"
        
        # 进行语义分块
        chunks = semantic_chunk(prefixed_content)
        
        # 为每个分块添加类型信息
        for chunk in chunks:
            chunk['content_type'] = content_type
            chunk['source_page'] = item.get('page', 0)
            chunk['extraction_method'] = chunk_method
            vectorized_chunks.append(chunk)
    
    return vectorized_chunks
```

#### 2. 表格专用处理
```python
def process_table_for_rag(table_data: Dict) -> List[str]:
    """将表格数据转换为RAG友好的文本格式"""
    
    processed_texts = []
    
    # 方法1：行级描述
    for i, row in enumerate(table_data['data']):
        row_description = f"表格第{i+1}行数据："
        for key, value in row.items():
            if value and str(value).strip():
                row_description += f" {key}为{value}，"
        processed_texts.append(row_description.rstrip('，'))
    
    # 方法2：列级汇总
    if table_data['data']:
        columns = table_data['data'][0].keys()
        for col in columns:
            col_values = [str(row.get(col, '')) for row in table_data['data'] if row.get(col)]
            if col_values:
                col_summary = f"表格中{col}列包含以下数据：{', '.join(col_values[:10])}"
                if len(col_values) > 10:
                    col_summary += f"等共{len(col_values)}项"
                processed_texts.append(col_summary)
    
    # 方法3：保持原始Markdown格式
    processed_texts.append(f"[完整表格]\n{table_data['markdown']}")
    
    return processed_texts
```

### 方案三：智能内容融合

#### 1. 页面级内容整合
```python
def integrate_page_content(pdf_path: str) -> List[Dict]:
    """按页面整合所有类型的内容"""
    
    # 提取所有内容
    content_data = enhanced_extract_from_pdf(pdf_path)
    
    # 按页面组织内容
    pages_content = {}
    
    for item in content_data['all_content']:
        page_num = item.get('page', 1)
        if page_num not in pages_content:
            pages_content[page_num] = {
                'text': [],
                'tables': [],
                'images': []
            }
        
        if item['type'] == 'text':
            pages_content[page_num]['text'].append(item['content'])
        elif item['type'] == 'table':
            pages_content[page_num]['tables'].append(item)
        elif item['type'] == 'image_text':
            pages_content[page_num]['images'].append(item['content'])
    
    # 生成页面级综合描述
    integrated_chunks = []
    
    for page_num, content in pages_content.items():
        page_description = f"第{page_num}页内容：\n\n"
        
        # 添加文本内容
        if content['text']:
            page_description += "文本内容：\n" + "\n".join(content['text']) + "\n\n"
        
        # 添加表格内容
        if content['tables']:
            page_description += "表格内容：\n"
            for i, table in enumerate(content['tables']):
                page_description += f"表格{i+1}：\n{table['content']}\n\n"
        
        # 添加图片文字
        if content['images']:
            page_description += "图片文字：\n" + "\n".join(content['images']) + "\n\n"
        
        integrated_chunks.append({
            'id': f'page_{page_num}',
            'chunk': page_description.strip(),
            'method': 'page_integration',
            'page': page_num,
            'content_types': list(content.keys())
        })
    
    return integrated_chunks
```

## 🔧 实际集成到RagHop系统

### 修改后的文件处理函数
```python
def enhanced_process_single_file(file_path: str, enable_multimodal: bool = True) -> str:
    """增强的单文件处理，支持多模态内容提取"""
    
    try:
        if file_path.lower().endswith('.pdf'):
            if enable_multimodal:
                # 使用增强的多模态提取
                content_data = enhanced_extract_from_pdf(file_path)
                
                # 整合所有内容为文本
                all_text_parts = []
                
                # 基础文本
                if content_data['basic_text'].strip():
                    all_text_parts.append(content_data['basic_text'])
                
                # 表格内容
                for table in content_data['tables']:
                    table_text = f"\n[表格 - 页面{table['page']}]\n{table['markdown']}\n"
                    all_text_parts.append(table_text)
                
                # 图片OCR内容
                for image in content_data['images']:
                    image_text = f"\n[图片文字 - 页面{image['page']}]\n{image['ocr_text']}\n"
                    all_text_parts.append(image_text)
                
                text = "\n".join(all_text_parts)
            else:
                # 使用原有的简单提取
                text = extract_text_from_pdf(file_path)
        else:
            # 文本文件处理保持不变
            text = process_text_file(file_path)
        
        # 清理文本
        text = clean_text(text)
        return text
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {str(e)}")
        return f"处理文件 {file_path} 失败：{str(e)}"
```

## 📊 技术方案对比

### 方案对比表

| 方案 | 优势 | 劣势 | 适用场景 | 实现复杂度 |
|------|------|------|----------|------------|
| **纯文本提取** | 简单快速，稳定 | 信息丢失严重 | 纯文本文档 | 低 |
| **OCR + 表格提取** | 信息完整，结构保持 | 处理时间长，依赖多个库 | 复杂PDF文档 | 中 |
| **多模态向量化** | 最大化信息利用 | 复杂度高，资源消耗大 | 高质量要求场景 | 高 |
| **页面级整合** | 保持上下文关系 | 分块可能过大 | 需要页面完整性的场景 | 中 |

### 依赖库要求

```python
# 新增依赖
pip install tabula-py camelot-py[cv] pytesseract pillow
# 系统依赖
# Ubuntu: sudo apt-get install tesseract-ocr tesseract-ocr-chi-sim
# macOS: brew install tesseract tesseract-lang
```

## 🎯 推荐实施策略

### 阶段一：基础增强（立即可实施）
1. **添加表格提取**：集成tabula-py进行表格结构化提取
2. **改进文本清理**：保持表格的Markdown格式
3. **内容类型标记**：为不同类型内容添加标识

### 阶段二：OCR集成（中期目标）
1. **图片文字识别**：集成pytesseract进行OCR
2. **多语言支持**：支持中英文混合识别
3. **质量过滤**：过滤OCR质量差的结果

### 阶段三：智能融合（长期目标）
1. **多模态向量化**：不同内容类型的专门处理
2. **上下文保持**：页面级内容整合
3. **智能分块**：根据内容类型调整分块策略

## 💡 医疗领域特殊考虑

### 医疗文档特点
- **表格密集**：检验报告、药物清单、诊断标准
- **图表重要**：解剖图、流程图、统计图表
- **格式标准**：医疗报告有固定格式

### 针对性优化
```python
def medical_pdf_processing(pdf_path: str) -> Dict:
    """医疗PDF的专门处理"""
    
    # 1. 识别医疗表格模式
    medical_table_patterns = [
        r'检验项目.*参考值',
        r'药品名称.*用法用量',
        r'诊断.*ICD.*编码'
    ]
    
    # 2. 医疗图片OCR优化
    # 使用医疗专用OCR模型或配置
    
    # 3. 医疗术语保护
    # 确保医疗术语在处理过程中不被破坏
    
    return processed_content
```

这种多模态处理方案将显著提升RagHop系统对复杂PDF文档的处理能力，特别是在医疗领域的应用效果。
