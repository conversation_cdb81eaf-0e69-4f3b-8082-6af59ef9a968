# RagHop系统流程图集合

## 1. 系统函数逻辑关系图

```mermaid
graph TD
    %% 用户入口层
    A[Web界面用户输入] --> B[process_and_update_chat]
    B --> C[process_question_with_reasoning]
    
    %% 核心处理层
    C --> D{选择处理模式}
    D -->|多跳推理| E[multi_hop_generate_answer]
    D -->|简单检索| F[simple_generate_answer]
    D -->|并行处理| G[ask_question_parallel]
    
    %% 多跳推理核心
    E --> H[ReasoningRAG.__init__]
    H --> I[_load_resources]
    E --> J[retrieve_and_answer]
    J --> K[stream_retrieve_and_answer]
    
    %% 多跳推理流程
    K --> L[_vectorize_query]
    K --> M[_retrieve]
    K --> N[_generate_reasoning]
    K --> O[_synthesize_answer]
    
    %% 简单检索流程
    F --> P[vector_search]
    P --> Q[vectorize_query]
    P --> R[faiss.search]
    
    %% 并行处理流程
    G --> S[get_search_background]
    G --> T[vector_search]
    G --> U[generate_answer_from_deepseek]
    
    %% 文档处理层
    V[文件上传] --> W[process_and_index_files]
    W --> X[process_single_file]
    X --> Y[extract_text_from_pdf]
    X --> Z[clean_text]
    W --> AA[semantic_chunk]
    W --> BB[vectorize_file]
    BB --> CC[vectorize_query]
    W --> DD[build_faiss_index]
    
    %% 知识库管理层
    EE[知识库管理] --> FF[create_knowledge_base]
    EE --> GG[delete_knowledge_base]
    EE --> HH[get_knowledge_bases]
    EE --> II[get_kb_files]
    
    %% 工具函数层
    JJ[get_kb_paths] --> E
    JJ --> F
    KK[clean_text] --> Y
    KK --> AA
    
    %% 样式定义
    classDef userLayer fill:#e1f5fe
    classDef coreLayer fill:#f3e5f5
    classDef reasoningLayer fill:#e8f5e8
    classDef docLayer fill:#fff3e0
    classDef kbLayer fill:#fce4ec
    classDef utilLayer fill:#f1f8e9
    
    class A,B userLayer
    class C,D,E,F,G coreLayer
    class H,I,J,K,L,M,N,O reasoningLayer
    class V,W,X,Y,Z,AA,BB,CC,DD docLayer
    class EE,FF,GG,HH,II kbLayer
    class JJ,KK utilLayer
```

## 2. 多跳推理RAG函数调用时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as Web界面
    participant Core as 核心处理层
    participant RAG as ReasoningRAG
    participant Vec as 向量化模块
    participant Search as 检索模块
    participant LLM as 大语言模型
    
    User->>UI: 提交问题
    UI->>UI: process_and_update_chat()
    UI->>Core: process_question_with_reasoning()
    
    alt 多跳推理模式
        Core->>Core: multi_hop_generate_answer()
        Core->>RAG: ReasoningRAG.__init__()
        RAG->>RAG: _load_resources()
        Core->>RAG: retrieve_and_answer()
        RAG->>RAG: stream_retrieve_and_answer()
        
        loop 多跳推理循环
            RAG->>Vec: _vectorize_query(query)
            Vec-->>RAG: query_vector
            RAG->>Search: _retrieve(query_vector)
            Search-->>RAG: retrieved_chunks
            RAG->>LLM: _generate_reasoning()
            LLM-->>RAG: reasoning_result
            
            alt 信息不足
                RAG->>RAG: 生成后续查询
                Note over RAG: 继续下一跳推理
            else 信息充分
                RAG->>LLM: _synthesize_answer()
                LLM-->>RAG: final_answer
                Note over RAG: 结束推理循环
            end
        end
        
        RAG-->>Core: answer + debug_info
        Core-->>UI: 流式返回结果
        
    else 简单检索模式
        Core->>Core: simple_generate_answer()
        Core->>Search: vector_search()
        Search->>Vec: vectorize_query()
        Vec-->>Search: query_vector
        Search->>Search: faiss.search()
        Search-->>Core: search_results
        Core->>LLM: generate_answer_from_deepseek()
        LLM-->>Core: answer
        Core-->>UI: 返回结果
    end
    
    UI->>UI: update_status()
    UI-->>User: 显示答案
```

## 3. 文档处理流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as Web界面
    participant FileProc as 文件处理模块
    participant TextExt as 文本提取
    participant Chunk as 分块模块
    participant Vec as 向量化模块
    participant Index as 索引模块
    participant KB as 知识库
    
    User->>UI: 上传文件
    UI->>UI: process_upload_to_kb()
    UI->>FileProc: process_and_index_files()
    
    par 并行处理多个文件
        FileProc->>TextExt: process_single_file()
        
        alt PDF文件
            TextExt->>TextExt: extract_text_from_pdf()
        else 文本文件
            TextExt->>TextExt: 编码检测和解码
        end
        
        TextExt->>TextExt: clean_text()
        TextExt-->>FileProc: 清理后的文本
    end
    
    FileProc->>Chunk: semantic_chunk()
    Chunk->>Chunk: EnhancedSentenceSplitter
    Chunk->>Chunk: _split_text()
    Chunk-->>FileProc: 分块结果
    
    FileProc->>Vec: vectorize_file()
    Vec->>Vec: 验证和清理分块
    
    loop 批量向量化
        Vec->>Vec: vectorize_query(batch)
        Note over Vec: API调用向量化服务
    end
    
    Vec-->>FileProc: 向量化数据
    
    FileProc->>Index: build_faiss_index()
    Index->>Index: 选择索引类型
    Index->>Index: 构建FAISS索引
    Index->>KB: 保存索引文件
    Index->>KB: 保存元数据
    Index-->>FileProc: 索引构建完成
    
    FileProc-->>UI: 处理结果
    UI->>UI: update_kb_files_list()
    UI-->>User: 显示处理结果
```

## 4. 数据持久化和向量化机制图

```mermaid
graph TD
    %% 文档上传和处理（一次性）
    A[用户上传文档] --> B[文本提取和清理]
    B --> C[智能语义分块]
    C --> D[文档向量化]
    D --> E[构建FAISS索引]
    E --> F[持久化存储]
    
    %% 持久化存储的文件
    F --> G[semantic_chunk.index<br/>FAISS索引文件]
    F --> H[semantic_chunk_metadata.json<br/>元数据文件]
    F --> I[原始文档文件<br/>PDF/TXT等]
    
    %% 系统重启
    J[系统重启/界面重新打开] --> K[扫描知识库目录]
    K --> L[检查索引文件存在性]
    L --> M[加载已有索引和元数据]
    
    %% 用户查询处理（每次）
    N[用户提问] --> O[查询向量化<br/>每次重新计算]
    O --> P[加载已保存的索引]
    P --> Q[向量相似度搜索]
    Q --> R[返回搜索结果]
    
    %% 多跳推理中的向量化
    S[多跳推理] --> T[原始查询向量化]
    T --> U[检索和推理分析]
    U --> V[生成后续查询]
    V --> W[后续查询向量化<br/>每次重新计算]
    W --> X[继续检索]
    
    %% 数据流向
    G -.->|加载| M
    H -.->|加载| M
    M -.->|使用| P
    
    %% 样式定义
    classDef oneTime fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    classDef persistent fill:#e3f2fd,stroke:#2196f3,stroke-width:2px
    classDef realTime fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    classDef storage fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px
    
    class A,B,C,D,E oneTime
    class G,H,I,M,P storage
    class N,O,T,V,W realTime
    class J,K,L persistent
```

## 5. 知识库管理流程图

```mermaid
graph TD
    %% 知识库创建流程
    A[创建知识库] --> B[输入知识库名称]
    B --> C[名称验证和净化]
    C --> D[创建知识库目录]
    D --> E[更新界面选择器]
    
    %% 文件上传流程
    F[文件上传] --> G[选择目标知识库]
    G --> H[文件格式验证]
    H --> I[并行文件处理]
    I --> J[文本提取]
    I --> K[智能分块]
    I --> L[向量化]
    I --> M[索引构建]
    M --> N[保存到知识库]
    
    %% 知识库删除流程
    O[删除知识库] --> P[确认删除操作]
    P --> Q[删除目录和文件]
    Q --> R[更新界面选择器]
    
    %% 知识库状态检查
    S[知识库状态检查] --> T[扫描知识库目录]
    T --> U[检查索引文件]
    U --> V[显示状态信息]
    
    %% 样式定义
    classDef createFlow fill:#e8f5e8
    classDef uploadFlow fill:#e3f2fd
    classDef deleteFlow fill:#ffebee
    classDef statusFlow fill:#fff3e0
    
    class A,B,C,D,E createFlow
    class F,G,H,I,J,K,L,M,N uploadFlow
    class O,P,Q,R deleteFlow
    class S,T,U,V statusFlow
```

## 使用说明

这些流程图展示了RagHop系统的核心工作流程：

1. **系统函数逻辑关系图**：展示了所有函数之间的调用关系和层次结构
2. **多跳推理时序图**：详细展示了多跳推理RAG的执行过程
3. **文档处理时序图**：展示了文档上传、处理、向量化和索引构建的完整流程
4. **数据持久化机制图**：说明了哪些数据会持久化保存，哪些需要实时计算
5. **知识库管理流程图**：展示了知识库的创建、管理和维护流程

您可以将这些Mermaid图表代码复制到支持Mermaid的工具中（如GitHub、GitLab、Notion、或在线Mermaid编辑器）来查看可视化效果。
