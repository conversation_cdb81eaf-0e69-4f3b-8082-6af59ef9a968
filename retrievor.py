import requests
import json
import re
from lxml import etree
import chardet
import jieba.analyse
from text2vec import *
from config import Config

def search_bing(query):
    """
    联网搜索函数 - 通过Bing搜索引擎获取与查询相关的网页信息

    功能流程：
    1. 构建模拟浏览器的请求头，避免反爬虫检测
    2. 向Bing发送搜索请求，获取搜索结果页面
    3. 解析搜索结果页面，提取链接和标题
    4. 逐个访问搜索结果页面，抓取正文内容
    5. 过滤和清理抓取的内容，返回结构化数据

    Args:
        query (str): 用户的搜索查询词

    Returns:
        list: 包含搜索结果的列表，每个元素为字典格式：
              {'url': '网页链接', 'text': '网页正文', 'title': '网页标题'}

    作用：
        - 为RAG系统提供实时的外部知识补充
        - 获取知识库中可能缺失的最新信息
        - 扩展系统的知识覆盖范围
    """
    # 构建完整的浏览器请求头，模拟真实用户访问，避免反爬虫检测
    headers = {
        'Cookie': 'MUID=2CFCFC26663D64393955ED1C623D62A4; MUIDB=2CFCFC26663D64393955ED1C623D62A4; SRCHD=AF=S00028; SRCHUID=V=2&GUID=76DC1CA8309043BBAB81CFC4C47D76DD&dmnchg=1; _UR=QS=0&TQS=0; MicrosoftApplicationsTelemetryDeviceId=64c1979f-ee59-40a7-928e-b755865bc6ae; ABDEF=V=13&ABDV=13&MRNB=1696643925014&MRB=0; ANON=A=15BC3EC2F3AC041DAD2C715CFFFFFFFF&E=1d05&W=2; NAP=V=1.9&E=1cab&C=MnJiRko1YRJfqV6H22giKijH0-4G1Ub50-Cg7gnMPMN4QFF_OeDZsQ&W=2; PPLState=1; _HPVN=CS=eyJQbiI6eyJDbiI6NiwiU3QiOjAsIlFzIjowLCJQcm9kIjoiUCJ9LCJTYyI6eyJDbiI6NiwiU3QiOjAsIlFzIjowLCJQcm9kIjoiSCJ9LCJReiI6eyJDbiI6NiwiU3QiOjAsIlFzIjowLCJQcm9kIjoiVCJ9LCJBcCI6dHJ1ZSwiTXV0ZSI6dHJ1ZSwiTGFkIjoiMjAyMy0xMi0xMVQwMDowMDowMFoiLCJJb3RkIjowLCJHd2IiOjAsIlRucyI6MCwiRGZ0IjpudWxsLCJNdnMiOjAsIkZsdCI6MCwiSW1wIjoxNiwiVG9iYnMiOjB9; _EDGE_S=SID=2E9C7DC6F71A6094195D6E28F6C8614B; USRLOC=HS=1&ELOC=LAT=30.26828956604004|LON=120.13351440429688|N=%E8%A5%BF%E6%B9%96%E5%8C%BA%EF%BC%8C%E6%B5%99%E6%B1%9F%E7%9C%81|ELT=4|; _Rwho=u=d; _SS=SID=2E9C7DC6F71A6094195D6E28F6C8614B&R=64&RB=64&GB=0&RG=0&RP=61; KievRPSSecAuth=FABKBBRaTOJILtFsMkpLVWSG6AN6C/svRwNmAAAEgAAACFAPF0afImbrCASVm1xT1K+FiXTsQgaoq6RydL+Ge3FvFrIbHVbXa7m0OlZNQJT4P62pu6xUtDTqwGPUE13tWBwVPkK1RahHVaGuUSLfwbp5o2HeLnKa+hfc6+sJiYHnxklhiJAzdi/oBbiWdDkf+5A+C0Fbsxeo4pQDt+kmeKhWpMwijA0bVP5ISXdkrLsRv5jiq97srkAMWFHqqGboI70LdX7ahqSSiykzwFdA1np3WhYhURWQ4b3z6uV7nsZpth6lpdafGZ2YLWr0Zwpv1D210P04ovzbbzvuKGoeljS4/SvdX8QUoGONzn0f2OXAOPvsnZJctbwxH/tkembDlpN4liJDCYhlYgoKtg5nuLBNihk75VctLodAQhosDNYM9stJRzQlusK+aEbDQKAgXunPwB0iPq0ECEVmLIApOeXs7DEtj29Q8zuWiOmxXnddGDm4Tf0VWUVjAEfP/PKiiTLAAS/dwPgOslgEdpy3Pw6GQYo3z3dZ16mWuXYX53utgdkK4rtqRj/FmYiTRjL6scm7Ds0UJnVNxdJcFACadTOzNVEGBp2XIb6XEAWZThz21+JJCn325RXG+zwJyjaKI941n6CbQ8Z/dXgUYMBsn/gfdGV3/+vz05pIOtB1zmzkvwds5v4M/zTcf5fgqWwLjSbIBFscYA626llQwDS6LkKwyoB/EB3L0XgLnOFpoSSpk41L/q5e0GkLVxzPA5kZue0iLTNEXUu/KCGnPOBkK0iAZVS/bJPVa3ZBPBOODwXnAUR0s0W1hbHLDW4I1ZrMuujx08DU0/nhhiq0mFgwwxHrd4vE9xdecjlpyL78pzPf5LVAiCKaWZ/BnKqHCYHA4hiEg8ffC5eFwoA6JsL0wtvTSdaAPEcUs103Um9eje8nNKwvDtesqh93lOAbNCfkfC/zAdtsR0dWaZIsYdAeMNQE+6//zLDbGIe24WVsSdiwZqdmYI2ICxE+KqPY++Ei4gfgKt0GNyiAfK0qSfALb01POx95rWOyWSPd0ge6DwM5mHAZfTePR44vBfFdhvUYBg0+47nOzY53hcO/6unDb3u1/PLHM7+rlS+76yjrZ9Dl7cFXRNBINy5afDUY+kU9JQS6QTbc5EIQTynlWkGU38m43GtWXquitzrjHuC0mYsUbLQOuZ1kFWHQXF/4I/aaxU1k0uvEOttkIUkhXi5lKo9uLoPGdha+AIGcDz8icpdDnfAHHpChm0YB8K8lcL0foY6NCib+o+LCLfriZg9Nvtkc8s1+TWPvCvHZX4bZuXyN4tHoQiysRd6j0gyJpLR4yQr5iOyBUgkM9WWKzkFmnzVYlb4ec6wpowsw2643AHs5Ge1FDjzKw3TdSVnwB2dHFh7tdNW1ywYDAGhpv8SSvQ66448UANVqB1uKwxsD0mXJR/tjMy9OuiNR; _U=1S7ve-XVb_pOh5Iig5kQlQDI6wv9BNl9HiCEtz0dS6dNV_UrQUBmAFVEZx7pYNRTwRxGG8eASH_IDUlpJu04SCp8aeYlPHkU_-0xGzlVA3nTqaE9kSUyIm1UVQYovjbOrsh4SeBbU-wrjqz6HV2DeUKJiHyTwYlDeQ8bYboyqhB4-ER5PjMGcp8daGbur9ER2KSm-nJOeUqnWeIawk0BVyw; WLS=C=26d7831f7a57e7fd&N=; SRCHUSR=DOB=20220207&T=1703137884000&TPC=1703137888000&POEX=W; SRCHHPGUSR=SRCHLANG=zh-Hans&BZA=0&BRW=HTP&BRH=M&CW=975&CH=931&SW=1920&SH=1080&DPR=1&UTC=480&DM=0&HV=1703137888&WTS=***********&PRVCW=975&PRVCH=931&SCW=1164&SCH=2821&PV=10.0.0; WLID=mA9cZwKIoBbQ9h07pbmfeYJEn7iBxd5sk7A9mKFJf1dP4SWmtri4X9d1xcl06hKEVmEpT+5GB21NeHYv/uk3maNbHalTEB+UwCwfS7RdzoQ=; _RwBf=r=0&ilt=1&ihpd=1&ispd=0&rc=64&rb=64&gb=0&rg=0&pc=61&mtu=0&rbb=0.0&g=0&cid=&clo=0&v=1&l=2023-12-20T08:00:00.0000000Z&lft=0001-01-01T00:00:00.0000000&aof=0&o=0&p=bingcopilotwaitlist&c=MY00IA&t=3001&s=2023-03-20T09:14:27.6545941+00:00&ts=2023-12-21T05:51:26.7082437+00:00&rwred=0&wls=2&wlb=0&lka=0&lkt=0&aad=0&TH=&mta=0&e=CS-LRz6MT6YjyZDqHmn2zXGq0iVnD2Plg7iI7uA3t-iwF4TTPdW2rejPh5N_c6syhuNr1-uNgqm8vKVLqjaaig&A=15BC3EC2F3AC041DAD2C715CFFFFFFFF&wle=1&ccp=0&ard=0001-01-01T00:00:00.0000000; ipv6=hit=1703141490169&t=4',
        'Accept-Encoding': 'gzip, deflate',  # 支持压缩传输
        'Accept-Language': 'zh-CN,zh;q=0.9',  # 指定中文语言偏好
        'Accept': '*/*',  # 接受所有类型的响应
        'Referer': 'https://cn.bing.com/search?',  # 设置来源页面
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'  # 模拟Chrome浏览器
        }

    res = []  # 存储最终的搜索结果

    # 构建Bing搜索URL
    url = 'https://cn.bing.com/search?q=' + query + '&qs=n&form=QBRE'

    # 发送搜索请求
    r = requests.get(url, headers=headers)

    # 处理响应编码，确保正确解析中文内容
    try:
        encoding = chardet.detect(r.content)['encoding']  # 自动检测编码
        r.encoding = encoding
        dom = etree.HTML(r.content.decode(encoding))
    except:
        # 编码检测失败时使用默认解析
        dom = etree.HTML(r.content)

    url_list = []  # 存储搜索结果的URL和标题
    tmp_url = []   # 用于去重，避免重复抓取同一URL

    # 解析搜索结果页面，提取链接和标题（只采集第一页结果）
    for sel in dom.xpath('//ol[@id="b_results"]/li/h2'):  # 定位到搜索结果标题元素
        l = ''.join(sel.xpath('a/@href'))  # 提取链接
        title = ''.join(sel.xpath('a//text()')).split('-')[0].strip()  # 提取标题并清理

        # 过滤有效链接：必须是http链接，不重复，不是文档文件
        if 'http' in l and l not in tmp_url and 'doc.' not in l:
            url_list.append([l, title])
            tmp_url.append(l)

    # 逐个访问搜索结果页面，抓取正文内容
    for turl, title in url_list:
        try:
            # 访问目标页面，设置超时防止卡死
            tr = requests.get(turl, headers=headers, timeout=(5, 5))
            tdom = etree.HTML(tr.content.decode('utf-8'))

            # 提取页面中所有段落文本
            text = '\n'.join(tdom.xpath('//p/text()'))

            # 过滤内容过短的页面，确保有效信息
            if len(text) > 15:
                tmp = {
                    'url': turl,      # 页面链接
                    'text': text,     # 页面正文
                    'title': title    # 页面标题
                }
                res.append(tmp)
        except Exception as e:
            # 单个页面抓取失败不影响其他页面
            print(f"抓取页面失败 {turl}: {e}")
            pass

    return res




class TextRecallRank():
    """
    文本召回与排序核心类 - 实现多种检索策略的文本召回和排序功能

    主要功能：
    1. 支持关键词匹配和语义向量两种检索方式
    2. 实现两阶段召回：先召回文档，再召回文本片段
    3. 提供智能文本分段和相关性评分功能
    4. 为RAG系统提供高质量的检索结果
    """

    def __init__(self, cfg):
        """
        初始化检索器配置参数

        Args:
            cfg: 配置对象，包含所有检索相关参数

        配置参数说明：
            topk: query关键词召回的数量 (默认5)
            topd: 召回文章的数量 (默认3)
            topt: 召回文本片段的数量 (默认6)
            maxlen: 召回文本片段的最大长度 (默认128)
            recall_way: 召回方式 ('keyword' 或 'embed')
        """
        self.topk = cfg.topk        # query关键词召回的数量
        self.topd = cfg.topd        # 召回文章的数量
        self.topt = cfg.topt        # 召回文本片段的数量
        self.maxlen = cfg.maxlen    # 召回文本片段的长度
        self.recall_way = cfg.recall_way  # 召回方式：keyword/embed



    def query_analyze(self, query):
        """
        查询分析函数 - 将自然语言查询转换为结构化的关键词表示

        功能流程：
        1. 使用jieba的TF-IDF算法从查询中提取关键词
        2. 计算权重归一化因子，统一不同查询的权重尺度
        3. 返回关键词列表和归一化因子

        Args:
            query (str): 用户的自然语言查询

        Returns:
            tuple: (keywords, total_weight)
                keywords: 包含关键词及其权重的列表 [(word, weight), ...]
                total_weight: 权重归一化因子，用于统一权重尺度

        作用：
            - 将自然语言转换为可计算的关键词表示
            - 为后续的关键词匹配提供基础
            - 通过权重体现不同词汇的重要性
        """
        # 使用jieba提取关键词，withWeight=True返回词汇和权重
        keywords = jieba.analyse.extract_tags(query, topK=self.topk, withWeight=True)

        # 计算归一化因子：topk / 所有权重之和，用于统一权重尺度
        total_weight = self.topk / sum([r[1] for r in keywords]) if keywords else 1.0

        return keywords, total_weight
    
    # # use other language
    # from keybert import KeyBERT

    # def query_analyze_en(self, query):
    #     """
    #     Use KeyBERT to extract English keywords from query
    #     """
    #     kw_model = KeyBERT()
    #     keywords = kw_model.extract_keywords(query, keyphrase_ngram_range=(1, 2), stop_words='english', top_n=self.topk)
    #     total_score = sum([score for _, score in keywords])
    #     total_weight = self.topk / total_score if total_score > 0 else 1.0
    #     return keywords, total_weight

    # from langdetect import detect

    # def query_analyze(self, query):
    #     lang = detect(query)
    #     if lang == 'zh-cn':
    #         return self.query_analyze_zh(query)
    #     else:
    #         return self.query_analyze_en(query)

        

    def text_segmentate(self, text, maxlen, seps='\n', strips=None):
        """
        智能文本分段函数 - 将长文本按照标点符号层次化分割为短片段

        功能流程：
        1. 按照分隔符优先级递归分割文本
        2. 确保每个片段不超过最大长度限制
        3. 在不超长的前提下尽可能保持语义完整性

        Args:
            text (str): 待分割的原始文本
            maxlen (int): 每个片段的最大长度
            seps (str): 分隔符字符串，按优先级排列 (如 '\n。；！？')
            strips (str): 需要去除的字符

        Returns:
            list: 分割后的文本片段列表

        分割策略：
            - 优先级：换行符 > 句号 > 分号 > 其他标点
            - 递归处理：如果按当前分隔符分割后仍超长，则使用下一级分隔符
            - 智能拼接：在不超长的前提下尽可能保持片段完整

        作用：
            - 将长文档分割为适合检索的短片段
            - 保持语义的相对完整性
            - 为后续的相似度计算提供合适的文本单元
        """
        text = text.strip().strip(strips) if strips else text.strip()

        # 如果文本长度超过限制且还有分隔符可用，进行分割
        if seps and len(text) > maxlen:
            pieces = text.split(seps[0])  # 按当前优先级最高的分隔符分割
            text, texts = '', []

            for i, p in enumerate(pieces):
                # 检查拼接后是否会超长
                if text and p and len(text) + len(p) > maxlen - 1:
                    # 递归处理当前累积的文本，使用下一级分隔符
                    texts.extend(self.text_segmentate(text, maxlen, seps[1:], strips))
                    text = ''

                # 拼接文本片段
                if i + 1 == len(pieces):
                    text = text + p  # 最后一个片段，不加分隔符
                else:
                    text = text + p + seps[0]  # 中间片段，保留分隔符

            # 处理剩余的文本
            if text:
                texts.extend(self.text_segmentate(text, maxlen, seps[1:], strips))
            return texts
        else:
            # 文本长度在限制内或无更多分隔符，直接返回
            return [text]

    def recall_title_score(self, title, keywords, total_weight):
        """
        标题匹配评分函数 - 计算查询关键词与文档标题的匹配程度

        功能流程：
        1. 遍历所有提取的关键词
        2. 检查每个关键词是否在标题中出现
        3. 累加匹配关键词的加权得分

        Args:
            title (str): 文档标题
            keywords (list): 关键词及权重列表 [(word, weight), ...]
            total_weight (float): 权重归一化因子

        Returns:
            float: 标题匹配得分，分数越高表示相关性越强

        评分策略：
            - 字面匹配：关键词必须在标题中完全出现
            - 权重累加：每个匹配的关键词贡献其权重分数
            - 归一化：使用total_weight统一不同查询的分数尺度

        作用：
            - 为文档级别的召回提供相关性评分
            - 实现粗粒度的文档筛选
            - 优先选择标题相关的文档
        """
        score = 0
        for item in keywords:
            kw, weight = item
            if kw in title:  # 检查关键词是否在标题中出现
                score += round(weight * total_weight, 4)  # 累加加权得分
        return score
    
    def recall_text_score(self, text, keywords, total_weight):
        """
        文本匹配评分函数 - 计算查询关键词与文本片段的匹配程度

        功能流程：
        1. 遍历所有提取的关键词
        2. 使用正则表达式查找关键词在文本中的出现
        3. 累加匹配关键词的加权得分

        Args:
            text (str): 待评分的文本片段
            keywords (list): 关键词及权重列表 [(word, weight), ...]
            total_weight (float): 权重归一化因子

        Returns:
            float: 文本匹配得分，分数越高表示相关性越强

        评分策略：
            - 正则匹配：使用正则表达式精确查找关键词
            - 权重累加：每个匹配的关键词贡献其权重分数
            - 当前实现：只考虑关键词是否出现，不考虑出现次数
            - 注释代码：可选择按出现次数加权 (len(pr))

        作用：
            - 为文本片段级别的召回提供相关性评分
            - 实现细粒度的文本筛选
            - 选择最相关的文本片段用于答案生成
        """
        score = 0
        for item in keywords:
            kw, weight = item
            # 构建正则表达式，查找关键词在文本中的出现位置
            p11 = re.compile('%s' % kw)
            pr = p11.findall(text)  # 获取所有匹配结果（可用于统计出现次数）

            # 当前评分策略：只要关键词出现就加分，不考虑出现次数
            # score += round(weight * total_weight, 4) * len(pr)  # 可选：按出现次数加权
            score += round(weight * total_weight, 4)  # 当前：只考虑是否出现
        return score
    
    def rank_text_by_keywords(self, query, data):
        """
        基于关键词的文本召回排序函数 - 使用关键词匹配进行两阶段检索

        功能流程：
        1. 查询分析：提取关键词和权重
        2. 文档召回：计算所有文档标题的匹配分数，选择top-d个文档
        3. 片段召回：在选中文档中分割文本，计算片段匹配分数，选择top-t个片段
        4. 结果整合：将最相关的文本片段拼接返回

        Args:
            query (str): 用户查询
            data (list): 搜索结果数据，格式：[{'title': '', 'text': '', 'url': ''}, ...]

        Returns:
            str: 拼接后的最相关文本片段，用换行符分隔

        检索策略：
            - 两阶段召回：先筛选文档，再筛选片段，提高效率
            - 关键词匹配：基于字面匹配，精确但可能遗漏同义词
            - 分数排序：按相关性分数降序排列
            - 长度过滤：过滤掉过短的文本片段（<20字符）

        作用：
            - 实现传统的关键词检索
            - 为用户查询找到最相关的文本内容
            - 作为语义检索的补充方案
        """

        # 第一步：查询分析 - 提取关键词和权重
        keywords, total_weight = self.query_analyze(query)

        # 第二步：文档级召回 - 基于标题匹配筛选文档
        title_score = {}
        for line in data:
            title = line['title']
            title_score[title] = self.recall_title_score(title, keywords, total_weight)

        # 按分数排序，选择top-d个最相关的文档
        title_score = sorted(title_score.items(), key=lambda x: x[1], reverse=True)
        recall_title_list = [t[0] for t in title_score[:self.topd]]

        # 第三步：片段级召回 - 在选中文档中进行细粒度检索
        sentence_score = {}
        for line in data:
            title = line['title']
            text = line['text']

            # 只处理在第一阶段被选中的文档
            if title in recall_title_list:
                # 智能分段：按换行符和句号分割文本
                for ct in self.text_segmentate(text, self.maxlen, seps='\n。'):
                    ct = re.sub('\s+', ' ', ct)  # 清理多余空白字符

                    # 过滤过短的片段，确保内容质量
                    if len(ct) >= 20:
                        sentence_score[ct] = self.recall_text_score(ct, keywords, total_weight)

        # 按分数排序，选择top-t个最相关的文本片段
        sentence_score = sorted(sentence_score.items(), key=lambda x: x[1], reverse=True)
        recall_sentence_list = [s[0] for s in sentence_score[:self.topt]]

        # 返回拼接后的结果
        return '\n'.join(recall_sentence_list)

    def rank_text_by_text2vec(self, query, data):
        """
        基于语义向量的文本召回排序函数 - 使用向量相似度进行两阶段检索

        功能流程：
        1. 数据验证：检查输入数据的有效性
        2. 标题向量化：将查询和所有标题转换为向量表示
        3. 标题相似度计算：计算查询与标题的余弦相似度
        4. 文档召回：选择最相似的top-d个文档
        5. 句子向量化：将查询和选中文档的句子转换为向量
        6. 句子相似度计算：计算查询与句子的余弦相似度
        7. 片段召回：选择最相似的top-t个句子

        Args:
            query (str): 用户查询
            data (list): 搜索结果数据，格式：[{'title': '', 'text': '', 'url': ''}, ...]

        Returns:
            str: 拼接后的最相关文本片段，用换行符分隔

        检索策略：
            - 语义理解：基于向量相似度，能捕获语义相关但词汇不同的内容
            - 两阶段召回：先筛选文档，再筛选句子，提高效率和准确性
            - 余弦相似度：使用标准化的相似度度量
            - 容错处理：完善的边界条件检查和错误处理

        优势：
            - 语义匹配：能理解同义词和语义变化
            - 鲁棒性强：完善的错误处理机制
            - 效果更好：通常比关键词匹配效果更佳

        作用：
            - 实现先进的语义检索
            - 为用户查询找到语义最相关的内容
            - 作为关键词检索的升级方案
        """
        # 数据有效性检查
        if not data:
            print("Warning: No data provided for ranking")
            return ""

        # 第一步：构建标题列表进行向量化
        # 将查询放在第一位，用于后续相似度计算
        title_list = [query]
        for line in data:
            title = line['title']
            title_list.append(title)

        # 确保至少有两个标题，否则无法进行相似度计算
        if len(title_list) <= 1:
            print("Warning: Not enough titles for similarity calculation")
            return ""

        # 第二步：标题向量化
        title_vectors = get_vector(title_list, 8)

        # 检查向量化是否成功
        if title_vectors.numel() == 0 or title_vectors.size(0) <= 1:
            print("Warning: Title vectorization failed or returned insufficient vectors")
            return ""

        # 第三步：计算标题相似度
        title_score = get_sim(title_vectors)

        # 检查相似度计算是否成功
        if not title_score:
            print("Warning: Title similarity calculation failed")
            return ""

        # 第四步：标题召回 - 构建相似度到索引的映射并排序
        title_score = dict(zip(title_score, range(1, len(title_list))))
        title_score = sorted(title_score.items(), key=lambda x: x[0], reverse=True)

        # 确保有足够的标题用于召回
        if not title_score or self.topd <= 0:
            print("Warning: No title scores or invalid topd parameter")
            return ""

        # 选择top-d个最相似的文档标题
        recall_title_list = [title_list[t[1]] for t in title_score[:min(self.topd, len(title_score))]]

        # 第五步：构建句子列表进行向量化
        # 将查询放在第一位，用于后续相似度计算
        sentence_list = [query]
        for line in data:
            title = line['title']
            text = line['text']

            # 只处理在第一阶段被选中的文档
            if title in recall_title_list:
                # 智能分段：按换行符和句号分割文本
                for ct in self.text_segmentate(text, self.maxlen, seps='\n。'):
                    ct = re.sub('\s+', ' ', ct)  # 清理多余空白字符

                    # 过滤过短的片段，确保内容质量
                    if len(ct) >= 20:
                        sentence_list.append(ct)

        # 确保至少有两个句子，否则无法进行相似度计算
        if len(sentence_list) <= 1:
            print("Warning: Not enough sentences for similarity calculation")
            return ""

        # 第六步：句子向量化
        sentence_vectors = get_vector(sentence_list, 8)

        # 检查向量化是否成功
        if sentence_vectors.numel() == 0 or sentence_vectors.size(0) <= 1:
            print("Warning: Sentence vectorization failed or returned insufficient vectors")
            return ""

        # 第七步：计算句子相似度
        sentence_score = get_sim(sentence_vectors)

        # 检查相似度计算是否成功
        if not sentence_score:
            print("Warning: Sentence similarity calculation failed")
            return ""

        # 第八步：句子召回 - 构建相似度到索引的映射并排序
        sentence_score = dict(zip(sentence_score, range(1, len(sentence_list))))
        sentence_score = sorted(sentence_score.items(), key=lambda x: x[0], reverse=True)

        # 确保有足够的句子用于召回
        if not sentence_score or self.topt <= 0:
            print("Warning: No sentence scores or invalid topt parameter")
            return ""

        # 选择top-t个最相似的句子
        recall_sentence_list = [sentence_list[s[1]] for s in sentence_score[:min(self.topt, len(sentence_score))]]

        # 返回拼接后的结果
        return '\n'.join(recall_sentence_list)


    def query_retrieve(self, query):
        """
        统一查询检索接口 - 整合联网搜索和文本召回的完整检索流程

        功能流程：
        1. 联网搜索：通过Bing搜索引擎获取相关网页信息
        2. 方法选择：根据配置选择关键词或语义检索方式
        3. 文本召回：对搜索结果进行召回和排序
        4. 结果返回：返回最相关的背景文本信息

        Args:
            query (str): 用户查询

        Returns:
            str: 检索到的背景文本信息，用于后续的答案生成

        检索策略：
            - 外部知识获取：通过联网搜索获取实时信息
            - 灵活的召回方式：支持关键词和语义两种检索模式
            - 端到端处理：从搜索到召回的完整流程

        配置选项：
            - recall_way='keyword': 使用关键词匹配检索
            - recall_way='embed': 使用语义向量检索

        作用：
            - 为RAG系统提供外部知识补充
            - 获取知识库中可能缺失的最新信息
            - 支持实时信息检索和智能召回
        """
        # 第一步：利用搜索引擎获取相关信息
        data = search_bing(query)

        # 第二步：根据配置选择检索方式，对获取的信息进行召回与排序
        if self.recall_way == 'keyword':
            # 使用关键词匹配进行检索
            bg_text = self.rank_text_by_keywords(query, data)
        else:
            # 使用语义向量进行检索（默认方式）
            bg_text = self.rank_text_by_text2vec(query, data)

        return bg_text


# ==================== 模块级接口配置 ====================
# 创建全局配置和检索器实例，提供简洁的外部调用接口

cfg = Config()  # 实例化配置对象
trr = TextRecallRank(cfg)  # 创建文本召回排序器实例
q_searching = trr.query_retrieve  # 导出统一的查询检索接口

# 使用说明：
# 其他模块可以直接调用 q_searching(query) 进行检索
# 例如：background_info = q_searching("医疗问题查询")
