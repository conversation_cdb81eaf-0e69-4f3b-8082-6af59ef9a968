# retrievor.py 函数逻辑和作用详解

## 📋 模块概述

`retrievor.py` 是RagHop项目的**检索与排序核心模块**，主要负责联网搜索和文本召回排序功能，为RAG系统提供外部知识补充。

## 🔍 核心函数详解

### 1. **search_bing(query)** - 联网搜索函数

**功能**：通过Bing搜索引擎获取与查询相关的网页信息

**流程**：
1. 构建模拟浏览器的请求头，避免反爬虫检测
2. 向Bing发送搜索请求，获取搜索结果页面
3. 解析搜索结果页面，提取链接和标题
4. 逐个访问搜索结果页面，抓取正文内容
5. 过滤和清理抓取的内容，返回结构化数据

**输入**：`query (str)` - 用户的搜索查询词

**输出**：`list` - 包含搜索结果的列表，格式：`[{'url': '', 'text': '', 'title': ''}, ...]`

**作用**：
- 为RAG系统提供实时的外部知识补充
- 获取知识库中可能缺失的最新信息
- 扩展系统的知识覆盖范围

---

## 🏗️ TextRecallRank 类详解

### 类概述
文本召回与排序核心类，实现多种检索策略的文本召回和排序功能。

**主要功能**：
1. 支持关键词匹配和语义向量两种检索方式
2. 实现两阶段召回：先召回文档，再召回文本片段
3. 提供智能文本分段和相关性评分功能
4. 为RAG系统提供高质量的检索结果

### 2. **__init__(self, cfg)** - 初始化函数

**功能**：初始化检索器配置参数

**配置参数**：
- `topk = 5`：query关键词召回的数量
- `topd = 3`：召回文章的数量
- `topt = 6`：召回文本片段的数量
- `maxlen = 128`：召回文本片段的最大长度
- `recall_way = 'embed'`：召回方式（'keyword' 或 'embed'）

### 3. **query_analyze(self, query)** - 查询分析函数

**功能**：将自然语言查询转换为结构化的关键词表示

**流程**：
1. 使用jieba的TF-IDF算法从查询中提取关键词
2. 计算权重归一化因子，统一不同查询的权重尺度
3. 返回关键词列表和归一化因子

**输入**：`query (str)` - 用户的自然语言查询

**输出**：`tuple` - `(keywords, total_weight)`
- `keywords`：包含关键词及其权重的列表 `[(word, weight), ...]`
- `total_weight`：权重归一化因子

**作用**：
- 将自然语言转换为可计算的关键词表示
- 为后续的关键词匹配提供基础
- 通过权重体现不同词汇的重要性

### 4. **text_segmentate(self, text, maxlen, seps, strips)** - 智能文本分段函数

**功能**：将长文本按照标点符号层次化分割为短片段

**流程**：
1. 按照分隔符优先级递归分割文本
2. 确保每个片段不超过最大长度限制
3. 在不超长的前提下尽可能保持语义完整性

**分割策略**：
- 优先级：换行符 > 句号 > 分号 > 其他标点
- 递归处理：如果按当前分隔符分割后仍超长，则使用下一级分隔符
- 智能拼接：在不超长的前提下尽可能保持片段完整

**输入**：
- `text (str)`：待分割的原始文本
- `maxlen (int)`：每个片段的最大长度
- `seps (str)`：分隔符字符串，按优先级排列
- `strips (str)`：需要去除的字符

**输出**：`list` - 分割后的文本片段列表

**作用**：
- 将长文档分割为适合检索的短片段
- 保持语义的相对完整性
- 为后续的相似度计算提供合适的文本单元

### 5. **recall_title_score(self, title, keywords, total_weight)** - 标题匹配评分函数

**功能**：计算查询关键词与文档标题的匹配程度

**流程**：
1. 遍历所有提取的关键词
2. 检查每个关键词是否在标题中出现
3. 累加匹配关键词的加权得分

**评分策略**：
- 字面匹配：关键词必须在标题中完全出现
- 权重累加：每个匹配的关键词贡献其权重分数
- 归一化：使用total_weight统一不同查询的分数尺度

**作用**：
- 为文档级别的召回提供相关性评分
- 实现粗粒度的文档筛选
- 优先选择标题相关的文档

### 6. **recall_text_score(self, text, keywords, total_weight)** - 文本匹配评分函数

**功能**：计算查询关键词与文本片段的匹配程度

**流程**：
1. 遍历所有提取的关键词
2. 使用正则表达式查找关键词在文本中的出现
3. 累加匹配关键词的加权得分

**评分策略**：
- 正则匹配：使用正则表达式精确查找关键词
- 权重累加：每个匹配的关键词贡献其权重分数
- 当前实现：只考虑关键词是否出现，不考虑出现次数

**作用**：
- 为文本片段级别的召回提供相关性评分
- 实现细粒度的文本筛选
- 选择最相关的文本片段用于答案生成

### 7. **rank_text_by_keywords(self, query, data)** - 基于关键词的文本召回排序函数

**功能**：使用关键词匹配进行两阶段检索

**流程**：
1. 查询分析：提取关键词和权重
2. 文档召回：计算所有文档标题的匹配分数，选择top-d个文档
3. 片段召回：在选中文档中分割文本，计算片段匹配分数，选择top-t个片段
4. 结果整合：将最相关的文本片段拼接返回

**检索策略**：
- 两阶段召回：先筛选文档，再筛选片段，提高效率
- 关键词匹配：基于字面匹配，精确但可能遗漏同义词
- 分数排序：按相关性分数降序排列
- 长度过滤：过滤掉过短的文本片段（<20字符）

**输入**：
- `query (str)`：用户查询
- `data (list)`：搜索结果数据

**输出**：`str` - 拼接后的最相关文本片段，用换行符分隔

**作用**：
- 实现传统的关键词检索
- 为用户查询找到最相关的文本内容
- 作为语义检索的补充方案

### 8. **rank_text_by_text2vec(self, query, data)** - 基于语义向量的文本召回排序函数

**功能**：使用向量相似度进行两阶段检索

**流程**：
1. 数据验证：检查输入数据的有效性
2. 标题向量化：将查询和所有标题转换为向量表示
3. 标题相似度计算：计算查询与标题的余弦相似度
4. 文档召回：选择最相似的top-d个文档
5. 句子向量化：将查询和选中文档的句子转换为向量
6. 句子相似度计算：计算查询与句子的余弦相似度
7. 片段召回：选择最相似的top-t个句子

**检索策略**：
- 语义理解：基于向量相似度，能捕获语义相关但词汇不同的内容
- 两阶段召回：先筛选文档，再筛选句子，提高效率和准确性
- 余弦相似度：使用标准化的相似度度量
- 容错处理：完善的边界条件检查和错误处理

**优势**：
- 语义匹配：能理解同义词和语义变化
- 鲁棒性强：完善的错误处理机制
- 效果更好：通常比关键词匹配效果更佳

**作用**：
- 实现先进的语义检索
- 为用户查询找到语义最相关的内容
- 作为关键词检索的升级方案

### 9. **query_retrieve(self, query)** - 统一查询检索接口

**功能**：整合联网搜索和文本召回的完整检索流程

**流程**：
1. 联网搜索：通过Bing搜索引擎获取相关网页信息
2. 方法选择：根据配置选择关键词或语义检索方式
3. 文本召回：对搜索结果进行召回和排序
4. 结果返回：返回最相关的背景文本信息

**配置选项**：
- `recall_way='keyword'`：使用关键词匹配检索
- `recall_way='embed'`：使用语义向量检索

**输入**：`query (str)` - 用户查询

**输出**：`str` - 检索到的背景文本信息

**作用**：
- 为RAG系统提供外部知识补充
- 获取知识库中可能缺失的最新信息
- 支持实时信息检索和智能召回

---

## 🔧 模块级接口

```python
cfg = Config()  # 实例化配置对象
trr = TextRecallRank(cfg)  # 创建文本召回排序器实例
q_searching = trr.query_retrieve  # 导出统一的查询检索接口
```

**使用方式**：
```python
# 其他模块可以直接调用
background_info = q_searching("医疗问题查询")
```

---

## 📊 功能特性对比

| 特性 | 关键词检索 | 语义检索 | 效果 |
|------|------------|----------|------|
| **匹配方式** | 字面匹配 | 语义相似度 | 互补性强 |
| **处理能力** | 精确匹配 | 模糊匹配 | 覆盖不同需求 |
| **计算复杂度** | 低 | 高 | 性能权衡 |
| **语义理解** | 弱 | 强 | 智能程度不同 |
| **实时性** | 快 | 较慢 | 响应速度差异 |

---

## 🎯 在RagHop系统中的作用

1. **外部知识补充**：通过联网搜索获取最新信息
2. **信息过滤**：从大量搜索结果中筛选最相关内容
3. **多模式支持**：提供关键词和语义两种检索方式
4. **实时更新**：确保知识库之外的信息及时性

---

## 💡 设计亮点

1. **双重检索策略**：关键词+语义检索的组合
2. **两阶段召回**：先文档后片段的层次化检索
3. **智能文本分段**：保持语义完整性的分割算法
4. **完善的错误处理**：robust的边界条件检查
5. **模拟真实用户**：完整的浏览器headers避免反爬

这个模块是RagHop系统的**外部知识获取引擎**，为多跳推理提供了丰富的背景信息，是实现智能问答的重要组成部分。
