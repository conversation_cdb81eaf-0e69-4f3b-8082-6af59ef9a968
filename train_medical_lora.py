#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为RagHop系统训练医疗领域LoRA适配器
基于Qwen2-7B-Instruct模型进行医疗领域的LoRA微调
"""

import torch
import os
import json
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer, 
    TrainingArguments, 
    Trainer,
    DataCollatorForSeq2Seq
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import numpy as np
from typing import List, Dict, Any

class MedicalLoRATrainer:
    """为RagHop系统训练医疗领域LoRA适配器"""
    
    def __init__(self, 
                 base_model: str = "Qwen/Qwen2-7B-Instruct",
                 output_dir: str = "./medical_lora_adapter"):
        """
        初始化医疗LoRA训练器
        
        参数:
            base_model: 基础模型名称
            output_dir: 输出目录
        """
        self.base_model_name = base_model
        self.output_dir = output_dir
        
        print(f"🚀 初始化医疗LoRA训练器")
        print(f"📦 基础模型: {base_model}")
        print(f"📁 输出目录: {output_dir}")
        
        self._setup_model()
        
    def _setup_model(self):
        """设置模型和LoRA配置"""
        
        print("📥 加载基础模型...")
        
        # 1. 加载基础模型
        self.model = AutoModelForCausalLM.from_pretrained(
            self.base_model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        
        # 2. 加载分词器
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.base_model_name,
            trust_remote_code=True,
            padding_side="right"  # 训练时使用右侧padding
        )
        
        # 设置特殊token
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
        
        # 3. 配置LoRA参数
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            inference_mode=False,
            r=8,                    # rank，控制LoRA矩阵的秩
            lora_alpha=32,          # LoRA缩放参数，通常为r的2-4倍
            lora_dropout=0.1,       # dropout率，防止过拟合
            target_modules=[        # 目标模块，应用LoRA的层
                "q_proj", "k_proj", "v_proj", "o_proj",  # 注意力层
                "gate_proj", "up_proj", "down_proj"      # MLP层
            ],
            bias="none",            # 不训练bias参数
            fan_in_fan_out=False,
            init_lora_weights=True  # 初始化LoRA权重
        )
        
        # 4. 应用LoRA到模型
        self.model = get_peft_model(self.model, lora_config)
        
        # 5. 打印可训练参数信息
        self.model.print_trainable_parameters()
        
        print("✅ 模型和LoRA配置完成！")
    
    def _prepare_medical_training_data(self) -> List[Dict[str, str]]:
        """准备医疗训练数据"""
        
        print("📋 准备医疗训练数据...")
        
        # 医疗问答训练数据
        medical_qa_data = [
            {
                "instruction": "基于医疗知识回答问题",
                "input": "糖尿病的早期症状有哪些？",
                "output": "糖尿病的早期症状主要包括：\n1. **多饮（口渴）**：患者经常感到口渴，需要大量饮水\n2. **多尿（尿频）**：排尿次数增加，尿量增多\n3. **多食（容易饥饿）**：食欲增加，但体重可能下降\n4. **体重下降**：尽管食欲增加，但体重意外减轻\n5. **疲劳乏力**：感到持续的疲倦和虚弱\n6. **视力模糊**：血糖波动可能影响视力\n7. **伤口愈合缓慢**：小伤口或感染恢复时间延长\n\n如果出现这些症状，建议及时就医进行血糖检测和专业诊断。早期发现和治疗对于糖尿病管理非常重要。"
            },
            {
                "instruction": "基于医疗知识回答问题",
                "input": "高血压患者的饮食注意事项有哪些？",
                "output": "高血压患者的饮食建议如下：\n\n**限制类食物：**\n1. **低盐饮食**：每日盐摄入量控制在6克以下，避免腌制食品\n2. **限制饱和脂肪**：减少动物内脏、肥肉等高脂食物\n3. **控制酒精**：男性每日不超过25克，女性不超过15克\n4. **减少咖啡因**：限制咖啡、浓茶的摄入\n\n**推荐类食物：**\n1. **新鲜蔬果**：富含钾、镁等有益矿物质\n2. **全谷物**：燕麦、糙米等粗粮\n3. **低脂蛋白**：鱼类、瘦肉、豆类\n4. **坚果类**：适量摄入无盐坚果\n\n**总体原则：**\n- 控制总热量摄入，维持健康体重\n- 规律进餐，避免暴饮暴食\n- 多喝水，保持充足水分\n\n建议在医生或营养师指导下制定个性化饮食方案。"
            },
            {
                "instruction": "基于医疗知识回答问题",
                "input": "心脏病的预防措施有哪些？",
                "output": "心脏病的预防措施包括以下几个方面：\n\n**生活方式调整：**\n1. **戒烟限酒**：完全戒烟，限制酒精摄入\n2. **规律运动**：每周至少150分钟中等强度有氧运动\n3. **健康饮食**：低盐、低脂、高纤维饮食\n4. **控制体重**：维持健康的BMI指数（18.5-24.9）\n5. **充足睡眠**：每晚7-9小时优质睡眠\n6. **压力管理**：学会放松技巧，减少精神压力\n\n**定期监测：**\n1. **血压监测**：定期测量血压，控制在正常范围\n2. **血脂检查**：监测胆固醇和甘油三酯水平\n3. **血糖控制**：预防和控制糖尿病\n4. **定期体检**：及早发现心血管疾病风险因素\n\n**药物预防：**\n- 在医生指导下，高危人群可考虑预防性用药\n- 严格按医嘱服用降压、降脂等药物\n\n**特别提醒：**\n有家族史或其他高危因素的人群应更加重视预防，定期咨询心血管专科医生。"
            },
            {
                "instruction": "基于医疗知识回答问题",
                "input": "感冒和流感有什么区别？",
                "output": "感冒和流感的主要区别如下：\n\n**症状差异：**\n\n**普通感冒：**\n- 症状较轻，逐渐出现\n- 主要症状：鼻塞、流鼻涕、打喷嚏、轻微咳嗽\n- 很少发热，如有发热通常低于38°C\n- 全身症状轻微\n- 病程3-7天\n\n**流感：**\n- 症状较重，突然发作\n- 主要症状：高热（38.5°C以上）、头痛、肌肉酸痛、乏力\n- 可能伴有咳嗽、咽痛\n- 全身症状明显\n- 病程7-14天\n\n**病原体：**\n- **感冒**：多种病毒引起（鼻病毒、冠状病毒等）\n- **流感**：流感病毒引起（甲型、乙型）\n\n**传染性：**\n- **感冒**：传染性相对较弱\n- **流感**：传染性强，容易引起流行\n\n**治疗：**\n- **感冒**：主要是对症治疗，多休息、多喝水\n- **流感**：可使用抗病毒药物，重症需及时就医\n\n**预防：**\n- 流感可通过疫苗预防\n- 两者都要注意手卫生、避免接触患者\n\n如症状严重或持续不改善，建议及时就医诊断。"
            },
            {
                "instruction": "基于医疗知识回答问题",
                "input": "如何正确测量血压？",
                "output": "正确测量血压的方法如下：\n\n**测量前准备：**\n1. **休息5-10分钟**：避免运动、吸烟、饮咖啡后立即测量\n2. **排空膀胱**：避免憋尿影响血压\n3. **选择安静环境**：减少外界干扰\n4. **穿着合适**：穿宽松衣袖，便于袖带佩戴\n\n**正确姿势：**\n1. **坐位测量**：背部有支撑，双脚平放地面\n2. **手臂位置**：上臂与心脏同高，手掌向上\n3. **袖带位置**：绑在上臂，下缘距肘窝2-3厘米\n4. **袖带松紧**：能插入1-2个手指为宜\n\n**测量步骤：**\n1. **选择合适袖带**：袖带宽度应为上臂周长的40%\n2. **缓慢充气**：至桡动脉搏动消失后再升高20-30mmHg\n3. **缓慢放气**：每秒2-3mmHg的速度\n4. **记录读数**：收缩压（第一声）和舒张压（声音消失）\n\n**注意事项：**\n- 首次测量应测双臂，以后选择读数较高的一侧\n- 连续测量2-3次，间隔1-2分钟，取平均值\n- 每天同一时间测量，建议早晚各一次\n- 记录测量时间、数值和当时状况\n\n**异常情况：**\n- 如血压持续异常，应及时就医\n- 高血压患者应按医嘱定期监测\n\n正确的血压测量对于高血压的诊断和管理非常重要。"
            }
        ]
        
        # 可以添加更多医疗数据...
        # 实际应用中，这里应该加载更大规模的医疗问答数据集
        
        print(f"✅ 准备了 {len(medical_qa_data)} 条医疗训练数据")
        return medical_qa_data
    
    def _format_training_data(self, raw_data: List[Dict[str, str]]) -> Dataset:
        """格式化训练数据为模型可用格式"""
        
        print("🔄 格式化训练数据...")
        
        formatted_data = []
        
        for item in raw_data:
            # 构建完整的训练文本
            if item.get("input"):
                # 有输入的情况
                text = f"""<|im_start|>system
你是一个专业的医疗AI助手，请基于医学知识提供准确、安全的医疗信息。<|im_end|>
<|im_start|>user
{item['instruction']}

{item['input']}<|im_end|>
<|im_start|>assistant
{item['output']}<|im_end|>"""
            else:
                # 只有指令的情况
                text = f"""<|im_start|>system
你是一个专业的医疗AI助手，请基于医学知识提供准确、安全的医疗信息。<|im_end|>
<|im_start|>user
{item['instruction']}<|im_end|>
<|im_start|>assistant
{item['output']}<|im_end|>"""
            
            formatted_data.append({"text": text})
        
        # 转换为Dataset对象
        dataset = Dataset.from_list(formatted_data)
        
        print(f"✅ 格式化完成，共 {len(dataset)} 条数据")
        return dataset
    
    def _preprocess_function(self, examples):
        """数据预处理函数"""
        
        # 分词
        model_inputs = self.tokenizer(
            examples["text"],
            max_length=1024,        # 根据需要调整最大长度
            truncation=True,
            padding=True,
            return_tensors="pt"
        )
        
        # 设置labels（用于计算loss）
        model_inputs["labels"] = model_inputs["input_ids"].clone()
        
        return model_inputs
    
    def train_lora(self, 
                   num_epochs: int = 3,
                   batch_size: int = 2,
                   learning_rate: float = 1e-4,
                   gradient_accumulation_steps: int = 4):
        """
        训练LoRA适配器
        
        参数:
            num_epochs: 训练轮数
            batch_size: 批次大小
            learning_rate: 学习率
            gradient_accumulation_steps: 梯度累积步数
        """
        
        print("🚀 开始训练医疗LoRA适配器...")
        print(f"📊 训练参数:")
        print(f"   - 训练轮数: {num_epochs}")
        print(f"   - 批次大小: {batch_size}")
        print(f"   - 学习率: {learning_rate}")
        print(f"   - 梯度累积: {gradient_accumulation_steps}")
        
        # 1. 准备训练数据
        raw_data = self._prepare_medical_training_data()
        train_dataset = self._format_training_data(raw_data)
        
        # 2. 预处理数据
        train_dataset = train_dataset.map(
            self._preprocess_function,
            batched=True,
            remove_columns=train_dataset.column_names
        )
        
        # 3. 配置训练参数
        training_args = TrainingArguments(
            output_dir=self.output_dir,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=batch_size,
            gradient_accumulation_steps=gradient_accumulation_steps,
            learning_rate=learning_rate,
            
            # 优化器配置
            optim="adamw_torch",
            weight_decay=0.01,
            adam_beta1=0.9,
            adam_beta2=0.999,
            max_grad_norm=1.0,
            
            # 学习率调度
            lr_scheduler_type="cosine",
            warmup_ratio=0.1,
            
            # 保存和日志
            save_strategy="steps",
            save_steps=100,
            save_total_limit=2,
            logging_strategy="steps",
            logging_steps=10,
            
            # 内存优化
            dataloader_pin_memory=True,
            dataloader_num_workers=4,
            remove_unused_columns=False,
            
            # 混合精度训练
            fp16=True,
            
            # 其他
            report_to=None,  # 不使用wandb等
            overwrite_output_dir=True
        )
        
        # 4. 数据整理器
        data_collator = DataCollatorForSeq2Seq(
            tokenizer=self.tokenizer,
            model=self.model,
            padding=True,
            return_tensors="pt"
        )
        
        # 5. 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            tokenizer=self.tokenizer,
            data_collator=data_collator
        )
        
        # 6. 开始训练
        print("🔥 开始训练...")
        train_result = trainer.train()
        
        # 7. 保存模型
        print("💾 保存LoRA适配器...")
        trainer.save_model()
        trainer.save_state()
        
        # 8. 保存训练指标
        metrics = train_result.metrics
        trainer.log_metrics("train", metrics)
        trainer.save_metrics("train", metrics)
        
        print(f"✅ 训练完成！LoRA适配器已保存到: {self.output_dir}")
        print(f"📊 训练指标: {metrics}")
        
        return trainer


def main():
    """主训练函数"""
    
    print("🏥 RagHop医疗LoRA训练器")
    print("=" * 50)
    
    try:
        # 创建训练器
        trainer = MedicalLoRATrainer(
            base_model="Qwen/Qwen2-7B-Instruct",
            output_dir="./medical_lora_adapter"
        )
        
        # 开始训练
        trainer.train_lora(
            num_epochs=3,
            batch_size=2,
            learning_rate=1e-4,
            gradient_accumulation_steps=4
        )
        
        print("\n🎉 医疗LoRA适配器训练成功！")
        print("📁 适配器保存位置: ./medical_lora_adapter")
        print("🔧 现在可以在RagHop系统中使用这个适配器了")
        
    except Exception as e:
        print(f"❌ 训练失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
