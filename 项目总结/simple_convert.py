#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RagHop项目分析报告 - 简单转换工具
将Markdown转换为格式化的HTML，可以在浏览器中打开并打印为PDF
"""

import os
import sys
from pathlib import Path

def install_markdown():
    """安装markdown库"""
    try:
        import markdown
        print("markdown 库已安装")
        return True
    except ImportError:
        print("正在安装 markdown 库...")
        os.system("pip3 install markdown")
        try:
            import markdown
            return True
        except ImportError:
            print("markdown 安装失败")
            return False

def markdown_to_html(md_file_path, html_file_path):
    """将Markdown文件转换为HTML"""
    try:
        import markdown
        
        # 读取Markdown文件
        with open(md_file_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 配置Markdown扩展
        extensions = [
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.toc',
            'markdown.extensions.codehilite'
        ]
        
        # 转换为HTML
        md = markdown.Markdown(extensions=extensions)
        html_content = md.convert(md_content)
        
        # 添加CSS样式
        css_style = """
        <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
            background-color: #fff;
            font-size: 14px;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-top: 30px;
            font-size: 28px;
            page-break-after: avoid;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 25px;
            font-size: 22px;
            page-break-after: avoid;
        }
        
        h3 {
            color: #2c3e50;
            margin-top: 20px;
            font-size: 18px;
            page-break-after: avoid;
        }
        
        h4 {
            color: #7f8c8d;
            margin-top: 15px;
            font-size: 16px;
            page-break-after: avoid;
        }
        
        p {
            margin: 12px 0;
            text-align: justify;
        }
        
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            color: #e74c3c;
            font-size: 13px;
        }
        
        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            margin: 15px 0;
            page-break-inside: avoid;
        }
        
        pre code {
            background-color: transparent;
            padding: 0;
            color: #333;
            font-size: 12px;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
            page-break-inside: avoid;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th, td {
            padding: 8px 12px;
            text-align: left;
            font-size: 13px;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 15px 0;
            padding-left: 20px;
            color: #7f8c8d;
            font-style: italic;
        }
        
        ul, ol {
            margin: 15px 0;
            padding-left: 30px;
        }
        
        li {
            margin: 5px 0;
        }
        
        .print-instructions {
            background-color: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .print-instructions h3 {
            color: #0c5460;
            margin-top: 0;
        }
        
        .architecture-diagram {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            page-break-inside: avoid;
        }
        
        .code-snippet {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            page-break-inside: avoid;
        }
        
        .feature-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        /* 打印优化 */
        @media print {
            body {
                margin: 1cm;
                font-size: 12px;
            }
            
            h1 { font-size: 20px; }
            h2 { font-size: 18px; }
            h3 { font-size: 16px; }
            h4 { font-size: 14px; }
            
            .print-instructions {
                display: none;
            }
            
            pre, .architecture-diagram {
                font-size: 10px;
            }
            
            table {
                font-size: 11px;
            }
            
            th, td {
                padding: 6px 8px;
            }
        }
        </style>
        """
        
        # 完整的HTML文档
        full_html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>RagHop项目架构分析报告</title>
            {css_style}
        </head>
        <body>
            <div class="print-instructions no-print">
                <h3>📄 如何导出为PDF</h3>
                <p><strong>方法1 - 浏览器打印：</strong></p>
                <ol>
                    <li>在浏览器中按 <code>Ctrl+P</code> (Windows) 或 <code>Cmd+P</code> (Mac)</li>
                    <li>选择"另存为PDF"或"打印到PDF"</li>
                    <li>在打印设置中选择"更多设置"</li>
                    <li>勾选"背景图形"以保留样式</li>
                    <li>点击"保存"或"打印"</li>
                </ol>
                <p><strong>方法2 - 在线转换：</strong></p>
                <p>将此HTML文件上传到在线HTML转PDF工具，如：</p>
                <ul>
                    <li><a href="https://www.ilovepdf.com/html-to-pdf" target="_blank">ILovePDF</a></li>
                    <li><a href="https://smallpdf.com/html-to-pdf" target="_blank">SmallPDF</a></li>
                    <li><a href="https://html-pdf-converter.com/" target="_blank">HTML PDF Converter</a></li>
                </ul>
            </div>
            
            {html_content}
        </body>
        </html>
        """
        
        # 保存HTML文件
        with open(html_file_path, 'w', encoding='utf-8') as f:
            f.write(full_html)
        
        print(f"✅ HTML文件已生成: {html_file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Markdown转HTML失败: {e}")
        return False

def main():
    """主函数"""
    # 检查并安装依赖
    if not install_markdown():
        return
    
    # 文件路径
    current_dir = Path.cwd()
    md_file = current_dir / "RagHop项目架构分析报告.md"
    html_file = current_dir / "RagHop项目架构分析报告.html"
    
    # 检查Markdown文件是否存在
    if not md_file.exists():
        print(f"❌ 错误: 找不到Markdown文件 {md_file}")
        return
    
    print("🚀 开始转换过程...")
    print(f"📄 输入文件: {md_file}")
    print(f"🌐 输出文件: {html_file}")
    
    # 转换为HTML
    if markdown_to_html(md_file, html_file):
        print("\n🎉 转换完成!")
        print(f"📁 HTML文件位置: {html_file}")
        print("\n📋 接下来的步骤:")
        print("1. 在浏览器中打开生成的HTML文件")
        print("2. 按照页面顶部的说明导出为PDF")
        print("3. 或者使用在线工具将HTML转换为PDF")
        
        # 尝试在默认浏览器中打开文件
        try:
            import webbrowser
            webbrowser.open(f'file://{html_file.absolute()}')
            print("\n🌐 已在默认浏览器中打开HTML文件")
        except:
            print(f"\n💡 请手动在浏览器中打开: {html_file.absolute()}")
    else:
        print("\n❌ 转换失败")

if __name__ == "__main__":
    main()
